import Link from 'next/link'
import { getWelcomeBanner } from '@/network/server-utils/getters'
import { sanitizeHtml } from '@/utils/sanitizeHtml'
import { getWelcomeOfferCardContent } from '@components/Banner/helpers'
import { DynamicLink } from '@components/DynamicLink'
import ThemedImage from '@components/ThemedImage/ThemedImage'
import type { Locale } from '@constants/locale'
import { Button } from '@heroui/button'
import styles from '@components/Banner/Banner.module.scss'

async function Banner({ locale }: { locale: Locale }) {
  const welcomeBanner = await getWelcomeBanner(locale)
  const bannerItems = welcomeBanner?.bannerItems
  const bannerConfig = bannerItems ? getWelcomeOfferCardContent({ bannerItems, isBanner: true }) : null

  return (
    <div className={styles.banner}>
      {!!bannerConfig?.image && (
        <ThemedImage
          lightSrc={'/images/demo/banner.png'}
          darkSrc={'/images/demo/banner_dark.png'}
          preload="both"
          alt="Banner"
          fill
          style={{ objectFit: 'cover' }}
          sizes="90vw"
          priority
          fetchPriority="high"
        />
      )}
      <div className={styles.bannerOverlay}>
        <div className={styles.bannerContent}>
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.label) }} />
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.title) }} />
          <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.subtitle) }} />
          <Button
            href={bannerConfig?.button?.link || '#'}
            as={DynamicLink}
            LinkComponent={Link}
            color={bannerConfig?.button?.type === 'secondary' ? 'secondary' : 'primary'}>
            <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(bannerConfig?.button?.name) }} />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Banner
