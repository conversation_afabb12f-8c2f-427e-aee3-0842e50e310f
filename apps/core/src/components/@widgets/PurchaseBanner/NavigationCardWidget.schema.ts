import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedNavigationCardConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.NAVIGATION_CARD),
  meta: z.object({
    title: z.string(),
    description: z.string().optional(),
    imageSrc: z.string().url().optional(),
    iconSrc: z.string().url().optional(),
    ctaLabel: z.string().optional(),
    ctaHref: z.string().optional(),
    ctaActionKey: z.string().optional(),
  }),
})

export type DynamicallyRenderedNavigationCardConfigType = z.infer<
  typeof DynamicallyRenderedNavigationCardConfigSchema
>['meta']
